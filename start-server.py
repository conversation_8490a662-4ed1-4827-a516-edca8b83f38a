#!/usr/bin/env python3
"""
Simple HTTP server for testing PWA functionality.
PWA features require serving files over HTTP/HTTPS, not file:// protocol.
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

# Configuration
PORT = 8000
HOST = 'localhost'

class PWAHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """Custom handler to add proper headers for PWA"""
    
    def end_headers(self):
        # Add security headers
        self.send_header('X-Content-Type-Options', 'nosniff')
        self.send_header('X-Frame-Options', 'DENY')
        self.send_header('X-XSS-Protection', '1; mode=block')
        
        # Add PWA-friendly headers
        if self.path.endswith('.json'):
            self.send_header('Content-Type', 'application/json')
        elif self.path.endswith('.js'):
            self.send_header('Content-Type', 'application/javascript')
        elif self.path.endswith('.html'):
            self.send_header('Content-Type', 'text/html; charset=utf-8')
        
        # Enable service worker for all origins (development only)
        self.send_header('Service-Worker-Allowed', '/')
        
        super().end_headers()
    
    def log_message(self, format, *args):
        """Custom log format"""
        print(f"[{self.log_date_time_string()}] {format % args}")

def main():
    # Change to the script directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    print(f"🚀 Starting PWA development server...")
    print(f"📁 Serving files from: {script_dir}")
    print(f"🌐 Server URL: http://{HOST}:{PORT}")
    print(f"📱 PWA Test Page: http://{HOST}:{PORT}/pwa-test.html")
    print(f"📄 PDF.js Test: http://{HOST}:{PORT}/test-pdfjs-print.html")
    print(f"🔄 Multi-Method Test: http://{HOST}:{PORT}/test-pdf-print.html")
    print()
    
    try:
        with socketserver.TCPServer((HOST, PORT), PWAHTTPRequestHandler) as httpd:
            print(f"✅ Server started successfully!")
            print(f"🔧 Press Ctrl+C to stop the server")
            print()
            print("📋 PWA Testing Checklist:")
            print("  1. Open browser dev tools (F12)")
            print("  2. Go to Application/Storage tab")
            print("  3. Check Service Workers section")
            print("  4. Check Manifest section")
            print("  5. Look for install prompt in address bar")
            print("  6. Test offline mode (Network tab > Offline)")
            print()
            
            # Try to open browser automatically
            try:
                webbrowser.open(f'http://{HOST}:{PORT}/pwa-test.html')
                print("🌐 Browser opened automatically")
            except:
                print("⚠️  Could not open browser automatically")
                print(f"   Please open: http://{HOST}:{PORT}/pwa-test.html")
            
            print()
            httpd.serve_forever()
            
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ Port {PORT} is already in use!")
            print(f"   Try a different port or stop the existing server")
            sys.exit(1)
        else:
            print(f"❌ Error starting server: {e}")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
        sys.exit(0)

if __name__ == '__main__':
    main()
