<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA Install Check - PDF Print Tools</title>
    
    <!-- PWA Meta Tags -->
    <meta name="description" content="Check PWA installation requirements">
    <meta name="theme-color" content="#2c3e50">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="PDF Print">
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">
    
    <link rel="stylesheet" href="shared-styles.css">
    <style>
        .check-container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .check-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .check-item {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .check-item:last-child {
            border-bottom: none;
        }
        
        .check-icon {
            width: 30px;
            font-size: 18px;
            margin-right: 15px;
        }
        
        .check-text {
            flex: 1;
        }
        
        .check-status {
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .status-pass {
            background: #d4edda;
            color: #155724;
        }
        
        .status-fail {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-warn {
            background: #fff3cd;
            color: #856404;
        }
        
        .install-button {
            background: #28a745;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 16px;
            border-radius: 8px;
            cursor: pointer;
            margin: 20px 0;
            display: none;
        }
        
        .install-button:hover {
            background: #218838;
        }
        
        .tips {
            background: #e7f3ff;
            border-left: 4px solid #2196F3;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-left">
            <h1>🔍 PWA Install Check</h1>
            <a href="test-pdfjs-print.html" class="nav-link">📄 PDF.js Test</a>
            <a href="test-pdf-print.html" class="nav-link">🔄 Multi-Method Test</a>
        </div>
    </div>
    
    <div class="check-container">
        <div class="check-section">
            <h2>📱 PWA Installation Requirements</h2>
            <p>This page checks if your browser and setup meet the requirements for PWA installation.</p>
            
            <button class="install-button" id="install-btn" onclick="installApp()">
                📱 Install App Now
            </button>
            
            <div id="checks-container">
                <!-- Checks will be populated by JavaScript -->
            </div>
        </div>
        
        <div class="check-section tips">
            <h3>💡 Tips to Enable Install Prompt</h3>
            <ul>
                <li><strong>Chrome/Edge:</strong> Visit the site multiple times, interact with it, then wait</li>
                <li><strong>Firefox:</strong> Limited PWA support, may not show install prompt</li>
                <li><strong>Safari:</strong> Use "Add to Home Screen" from share menu</li>
                <li><strong>Mobile:</strong> Look for "Add to Home Screen" option in browser menu</li>
                <li><strong>Desktop:</strong> Look for install icon in address bar or browser menu</li>
            </ul>
        </div>
        
        <div class="check-section">
            <h3>🛠️ Manual Installation Methods</h3>
            <div class="check-item">
                <div class="check-icon">🖥️</div>
                <div class="check-text">
                    <strong>Desktop Chrome/Edge:</strong> Look for install icon in address bar, or go to Menu → Install [App Name]
                </div>
            </div>
            <div class="check-item">
                <div class="check-icon">📱</div>
                <div class="check-text">
                    <strong>Mobile Chrome:</strong> Menu (⋮) → Add to Home screen
                </div>
            </div>
            <div class="check-item">
                <div class="check-icon">🍎</div>
                <div class="check-text">
                    <strong>Safari (iOS):</strong> Share button → Add to Home Screen
                </div>
            </div>
        </div>
    </div>
    
    <script>
        let deferredPrompt = null;
        const installBtn = document.getElementById('install-btn');
        const checksContainer = document.getElementById('checks-container');
        
        // Install app function
        async function installApp() {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                const { outcome } = await deferredPrompt.userChoice;
                console.log('Install prompt result:', outcome);
                
                if (outcome === 'accepted') {
                    addCheck('✅', 'User accepted install prompt', 'PASS');
                } else {
                    addCheck('❌', 'User dismissed install prompt', 'FAIL');
                }
                
                deferredPrompt = null;
                installBtn.style.display = 'none';
            } else {
                alert('Install prompt not available. Try the manual installation methods below.');
            }
        }
        
        function addCheck(icon, text, status) {
            const checkItem = document.createElement('div');
            checkItem.className = 'check-item';
            
            const statusClass = status === 'PASS' ? 'status-pass' : 
                               status === 'FAIL' ? 'status-fail' : 'status-warn';
            
            checkItem.innerHTML = `
                <div class="check-icon">${icon}</div>
                <div class="check-text">${text}</div>
                <div class="check-status ${statusClass}">${status}</div>
            `;
            
            checksContainer.appendChild(checkItem);
        }
        
        function runChecks() {
            checksContainer.innerHTML = '<h3>🔍 Checking Requirements...</h3>';
            
            // Check 1: HTTPS or localhost
            const isSecure = location.protocol === 'https:' || location.hostname === 'localhost' || location.hostname === '127.0.0.1';
            addCheck(isSecure ? '🔒' : '⚠️', 
                    `Secure context: ${location.protocol}//${location.hostname}`, 
                    isSecure ? 'PASS' : 'WARN');
            
            // Check 2: Service Worker support
            const swSupported = 'serviceWorker' in navigator;
            addCheck(swSupported ? '⚙️' : '❌', 
                    'Service Worker API support', 
                    swSupported ? 'PASS' : 'FAIL');
            
            // Check 3: Manifest support
            const manifestSupported = 'manifest' in document.createElement('link');
            addCheck(manifestSupported ? '📋' : '❌', 
                    'Web App Manifest support', 
                    manifestSupported ? 'PASS' : 'FAIL');
            
            // Check 4: Already installed
            const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
            addCheck(isStandalone ? '📱' : 'ℹ️', 
                    isStandalone ? 'App is already installed' : 'App not installed yet', 
                    isStandalone ? 'PASS' : 'WARN');
            
            // Check 5: Browser support
            const userAgent = navigator.userAgent;
            let browserSupport = 'WARN';
            let browserText = 'Unknown browser';
            
            if (userAgent.includes('Chrome') && !userAgent.includes('Edg')) {
                browserSupport = 'PASS';
                browserText = 'Chrome - Full PWA support';
            } else if (userAgent.includes('Edg')) {
                browserSupport = 'PASS';
                browserText = 'Edge - Full PWA support';
            } else if (userAgent.includes('Firefox')) {
                browserSupport = 'WARN';
                browserText = 'Firefox - Limited PWA support';
            } else if (userAgent.includes('Safari')) {
                browserSupport = 'WARN';
                browserText = 'Safari - Basic PWA support';
            }
            
            addCheck(browserSupport === 'PASS' ? '🌐' : '⚠️', browserText, browserSupport);
            
            // Check service worker registration
            if (swSupported) {
                navigator.serviceWorker.getRegistration().then(registration => {
                    addCheck(registration ? '✅' : '❌', 
                            registration ? 'Service Worker registered' : 'Service Worker not registered', 
                            registration ? 'PASS' : 'FAIL');
                });
            }
            
            // Check manifest
            fetch('./manifest.json')
                .then(response => response.ok ? response.json() : Promise.reject('Not found'))
                .then(manifest => {
                    addCheck('✅', `Manifest loaded: ${manifest.name}`, 'PASS');
                })
                .catch(() => {
                    addCheck('❌', 'Manifest file not accessible', 'FAIL');
                });
        }
        
        // Listen for install prompt
        window.addEventListener('beforeinstallprompt', (e) => {
            console.log('Install prompt available!');
            e.preventDefault();
            deferredPrompt = e;
            installBtn.style.display = 'block';
            addCheck('🎉', 'Install prompt is available!', 'PASS');
        });
        
        // Listen for successful installation
        window.addEventListener('appinstalled', () => {
            console.log('App installed successfully');
            addCheck('🎉', 'App installed successfully!', 'PASS');
            installBtn.style.display = 'none';
        });
        
        // Register service worker
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('./sw.js')
                .then(registration => {
                    console.log('SW registered:', registration);
                })
                .catch(error => {
                    console.log('SW registration failed:', error);
                });
        }
        
        // Run checks when page loads
        window.addEventListener('load', () => {
            setTimeout(runChecks, 500);
        });
    </script>
</body>
</html>
